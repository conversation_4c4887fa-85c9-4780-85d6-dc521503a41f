/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/ProgressBar/ProgressBar.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Saturday, April 29th 2023, 12:38:50 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

import './index.css';

const CustomProgressBar = (props) => {
  const { percentage } = props;

  return (
    <div className="progress-bar">
      <div className="progress" style={{ width: `${percentage}%` }}></div>
      <div className="progress-label">{percentage}%</div>
    </div>
  );
};

export default CustomProgressBar;
