/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/SkillSection/SkillSection.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Monday, July 24th 2023, 11:19:56 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

import Image from 'next/image';
import CustomProgressBar from '../CustomProgressBar/CustomProgressBar';
import Aws from '/Assets/images/icons/Aws.svg';
import Csharp from '/Assets/images/icons/Csharp.svg';
import Docker from '/Assets/images/icons/Docker.svg';
import Graphql from '/Assets/images/icons/Graphql.svg';
import JavaScript from '/Assets/images/icons/JavaScript.svg';
import Laravel from '/Assets/images/icons/Laravel.svg';
import MongoDB from '/Assets/images/icons/MongoDB.svg';
import NodeJs from '/Assets/images/icons/NodeJs.svg';
import PHP from '/Assets/images/icons/PHP.svg';
import PostgreSQL from '/Assets/images/icons/PostgreSQL.svg';
import Python from '/Assets/images/icons/Python.svg';
import ReactIC from '/Assets/images/icons/React.svg';
import TypeScript from '/Assets/images/icons/TypeScript.svg';
import ExpressJs from '/Assets/images/icons/ex.png';
// import Next from "/Assets/images/icons/next.png";
import './index.css';
import Nest from '/Assets/images/icons/nest.svg';
import Next from '/Assets/images/icons/next.svg';

export const staticSkillsData = [
  { name: "React", logo: ReactIC, achievedPercentage: 85 },
  { name: "Next Js", logo: Next, achievedPercentage: 70 },
  // { name: "Material UI", logo: MaterialUI, achievedPercentage: 80 },
  // { name: "Html", logo: Html5, achievedPercentage: 70 },
  // { name: "Css3", logo: Css3, achievedPercentage: 70 },
];
const backendData = [
  { name: "Nest Js", logo: Nest, achievedPercentage: 80 },
  { name: "Express Js", logo: ExpressJs, achievedPercentage: 85 },
  { name: "NodeJs", logo: NodeJs, achievedPercentage: 85 },
  { name: "Laravel", logo: Laravel, achievedPercentage: 60 },
];
const extraData = [
  { name: "JavaScript", logo: JavaScript, achievedPercentage: 90 },
  { name: "TypeScript", logo: TypeScript, achievedPercentage: 85 },
  { name: "MongoDB", logo: MongoDB, achievedPercentage: 85 },
  { name: "PostgreSQL", logo: PostgreSQL, achievedPercentage: 80 },
  { name: "Graphql", logo: Graphql, achievedPercentage: 70 },
  { name: "Docker", logo: Docker, achievedPercentage: 60 },
  { name: "AWS", logo: Aws, achievedPercentage: 60 },
  { name: "Python", logo: Python, achievedPercentage: 80 },
  { name: "Csharp", logo: Csharp, achievedPercentage: 80 },
  { name: "PHP", logo: PHP, achievedPercentage: 60 },
];
const SkillSection = ({ data }) => {
  // TODO : based on tag group the skills and show them in a better way
  return (
    <>
      <div id="skill" className=" flex lg:flex-row md:flex-row flex-col full-div">
        <div className="border-right left-skills-div">
          <div className="skillDiv">
            <h5 className="font-bold skill-title">back_end</h5>
            {data.map((item) => (
              <div key={item.name} className="flex lg:gap-6 my-2 items-center justify-between">
                <Image src={item.logo} alt={item.name} width={40} height={40} />
                <div>
                  <div>{item.name}</div>
                </div>
                <CustomProgressBar percentage={item.achievedPercentage} />
              </div>
            ))}
          </div>

          {/* <div className="skillDiv">
            <h5 className="font-bold skill-title">front_end</h5>
            {frontendData.map((item) => (
              <div key={item.name} className="flex lg:gap-6 my-2 items-center justify-between">
                <Image src={item.logo} alt={item.name} width={40} height={40} />
                <div>
                  <div>{item.name}</div>
                </div>
                <CustomProgressBar percentage={item.achievedPercentage} />
              </div>
            ))}
          </div> */}
        </div>
        {/* <div className="right-skills-div">
          <div className="skillDiv">
            <h5 className="font-bold skill-title">extra</h5>
            {extraData.map((item) => (
              <div key={item.name} className=" flex lg:gap-6 my-2 items-center justify-between ">
                <Image src={item.logo} alt={item.name} width={40} height={40} />
                <div>
                  <div>{item.name}</div>
                </div>
                <CustomProgressBar percentage={item.achievedPercentage} />
              </div>
            ))}
          </div>
        </div> */}

        {/* <ComingSoon /> */}
      </div>
    </>
  );
};

export default SkillSection;
