/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/MobileMenu/MobileMenu.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, July 13th 2023, 1:04:30 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzim Ahmed
 */
"use client";
import Image from 'next/image';
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import {useRef, useState} from 'react';
import burgerIcon from '../../Assets/icons/burger.svg';
import './index.css';

const MobileMenu = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const pathname = usePathname();
  const menuRef = useRef(null);

  const toggleMobileMenu = () => {
    setMenuOpen(!menuOpen);
    const menu = document.getElementById("menu");
    menu.classList.toggle("hidden");
    const page = document.getElementById("main");
    if (page.style.display === "none") {
      page.style.display = "flex";
    } else {
      page.style.display = "none";
    }
  };
 

  const goHome = () => {
    const menu = document.getElementById("menu");
    if (!menu.classList.contains("hidden")) {
      menu.classList.toggle("hidden");
      document.getElementById("main").style.display = "flex";
      setMenuOpen(!menuOpen);
    }
  };

  const checkActiveLink = (path) => {
    if (pathname === path) {
      return "nav-link-mobile.active";
    } else {
      return "";
    }
  };

  return (
    <>
      <div id="mobile-menu" ref={menuRef} className="w-full z-10 md:hidden">
        <div
          id="mobile-header"
          className="w-full h-16 flex justify-between items-center"
        >
          <Link
            href="/"
            className="text-menu-text font-fira_retina flex h-full items-center mx-5"
            onClick={goHome}
          >
            tanzim-ahmed
          </Link>
          {!menuOpen && (
            <Image
              alt="burger-icon"
              src={burgerIcon}
              className="w-5 h-5 mx-5 my-auto"
              onClick={toggleMobileMenu}
            ></Image>
          )}
        </div>
        <div id="menu" className="bg-mobile-menu-blue z-10 hidden">
          <Link
            id="nav-link-mobile"
            className={checkActiveLink("/hello")}
            href="/hello"
            onClick={toggleMobileMenu}
          >
            _hello
          </Link>
          <Link
            id="nav-link-mobile"
            className={checkActiveLink("/who_am_i/experience")}
            href="/who_am_i"
            onClick={toggleMobileMenu}
          >
            _who_am_i
          </Link>
          <Link
            id="nav-link-mobile"
            className={checkActiveLink("/who_am_i/projects")}
            href="/who_am_i/projects"
            onClick={toggleMobileMenu}
          >
            _projects
          </Link>
          <Link
            id="nav-link-mobile"
            className={checkActiveLink("/who_am_i/contact")}
            href="/who_am_i/contact"
            onClick={toggleMobileMenu}
          >
            _contact-me
          </Link>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
