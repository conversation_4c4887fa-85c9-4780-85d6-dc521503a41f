import Image from 'next/image';
import {useState} from 'react';
import burgerIcon from '../../Assets/icons/burger.svg';
import burgerCloseIcon from '../../Assets/icons/burger-close.svg';
import Link from 'next/link';
import './index.css';
import {usePathname} from 'next/navigation';

const MobileMenu = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const pathname = usePathname();

  const toggleMobileMenu = () => {
    setMenuOpen((prevState) => !prevState);
    // Only toggle the menu visibility, don't manipulate page content
    const menu = document.getElementById("menu");
    if (menu) {
      menu.classList.toggle("hidden");
    }
  };

  const goHome = () => {
    const menu = document.getElementById("menu");
    if (menu && !menu.classList.contains("hidden")) {
      menu.classList.toggle("hidden");
      setMenuOpen(false);
    }
  };

  const isActive = (route) => {
    return pathname === route;
  };

  return (
    <div id="mobile-menu" className="w-full z-10 md:hidden">
      <div
        id="mobile-header"
        className="w-full h-16 flex justify-between items-center"
      >
        <Link
          className="text-menu-text font-fira_retina flex h-full items-center mx-5"
          href="/"
          onClick={goHome}
        >
          tanzim-ahmed
        </Link>
        <Image
          src={burgerIcon}
          alt="burger-icon"
          style={{ display: !menuOpen ? "inline-block" : "none" }}
          onClick={toggleMobileMenu}
          className="w-5 h-5 mx-5 my-auto"
        />
        <Image
          src={burgerCloseIcon}
          alt="burger-icon"
          style={{ display: menuOpen ? "inline-block" : "none" }}
          onClick={toggleMobileMenu}
          name="icon-park-outline:close"
          className="w-5 h-5 mx-5 my-auto"
        />
      </div>

      <div
        id="menu"
        className={`bg-mobile-menu-blue z-10 ${menuOpen ? "" : "hidden"}`}
      >
        <Link
          href="/hello"
          className={isActive("/") ? "active-link" : "basic-link"}
          onClick={toggleMobileMenu}
        >
          _hello
        </Link>

        <Link
          href="/aboutme"
          className={isActive("/aboutme") ? "active-link" : "basic-link"}
          // className="active"
          onClick={toggleMobileMenu}
        >
          _about-me
        </Link>

        <Link
          href="/projects"
          className={isActive("/projects") ? "active-link" : "basic-link"}
          onClick={toggleMobileMenu}
        >
          _projects
        </Link>

        <Link
          href="/contact"
          className={isActive("/contact") ? "active-link" : "basic-link"}
          onClick={toggleMobileMenu}
        >
          _contact-me
        </Link>
      </div>
    </div>
  );
};

export default MobileMenu;
