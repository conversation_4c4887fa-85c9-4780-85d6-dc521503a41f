/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/Footer/Footer.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Wednesday, March 15th 2023, 7:23:28 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

import Image from 'next/image';
import Link from 'next/link';
import fb from '../../../Assets/icons/social/facebook.svg';
import github from '../../../Assets/icons/social/github.svg';
import twitter from '../../../Assets/icons/social/twitter.svg';
import {useCommonData} from '@/hooks/useCommonData';
import './index.css';

const Footer = () => {
  const {
    loading,
    error,
    getConfigGithubLink,
    getGithubUsername,
    getTwitterUsername,
    getFacebookUsername
  } = useCommonData();

  // Loading state
  if (loading) {
    return (
      <footer className="flex md:justify-between border-top text-menu-text font-fira_retina">
        <div className="w-full flex justify-between md:justify-start">
          <span
            id="social-title"
            className="h-full flex justify-center items-center border-right px-5"
          >
            find me in:
          </span>
          <div id="social-icons" className="flex">
            <div className="flex justify-center items-center w-12 h-10 border-right">
              <div className="w-5 h-5 bg-gray-300 animate-pulse rounded"></div>
            </div>
            <div className="flex justify-center items-center w-12 h-10 border-right">
              <div className="w-5 h-5 bg-gray-300 animate-pulse rounded"></div>
            </div>
            <div className="flex md:hidden justify-center items-center w-12 h-10">
              <div className="w-5 h-5 bg-gray-300 animate-pulse rounded"></div>
            </div>
          </div>
        </div>
        <div className="hidden md:flex items-center px-5 border-left">
          <div className="w-20 h-4 bg-gray-300 animate-pulse rounded mr-2"></div>
          <div className="w-5 h-5 bg-gray-300 animate-pulse rounded"></div>
        </div>
      </footer>
    );
  }

  // Error state - show defaults
  if (error) {
    console.warn('Footer: Error loading data, using defaults:', error);
  }

  // Get dynamic values with fallbacks
  const githubLink = getConfigGithubLink();
  const githubUsername = getGithubUsername();
  const twitterUsername = getTwitterUsername();
  const facebookUsername = getFacebookUsername();
  return (
    <>
      <footer className="flex md:justify-between border-top text-menu-text font-fira_retina">
        <div className="w-full flex justify-between md:justify-start">
          <span
            id="social-title"
            className="h-full flex justify-center items-center border-right px-5"
          >
            {" "}
            find me in:{" "}
          </span>
          <div id="social-icons" className="flex">
            <Link
              href={`https://twitter.com/${twitterUsername}`}
              target="_blank"
              className="flex justify-center items-center"
              title={`Follow @${twitterUsername} on Twitter`}
            >
              <Image alt="Twitter" src={twitter}></Image>
            </Link>
            <Link
              href={`https://facebook.com/${facebookUsername}`}
              target="_blank"
              className="flex justify-center items-center"
              title={`Follow ${facebookUsername} on Facebook`}
            >
              <Image alt="Facebook" src={fb}></Image>
            </Link>
            <Link
              href={githubLink}
              target="_blank"
              className="flex md:hidden justify-center items-center"
              title={`View @${githubUsername} on GitHub`}
            >
              <Image alt="GitHub" src={github}></Image>
            </Link>
          </div>
        </div>
        <Link
          href={githubLink}
          target="_blank"
          className="hidden md:flex items-center px-5 border-left"
          title={`View @${githubUsername} on GitHub`}
        >
          {" "}
          @{githubUsername} <Image alt="GitHub" src={github}></Image>
        </Link>
      </footer>
    </>
  );
};

export default Footer;
