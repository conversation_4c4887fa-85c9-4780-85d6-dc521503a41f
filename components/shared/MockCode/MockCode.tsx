/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/MockCode/MockCode.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Saturday, March 18th 2023, 12:03:30 am
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

import React from 'react';

const MockCode = () => {
  return (
    <div className="mockup-code">
      <pre data-prefix="$">
        <code>npm i daisyui</code>
      </pre>
      <pre data-prefix=">" className="text-warning">
        <code>installing...</code>
      </pre>
      <pre data-prefix=">" className="text-success">
        <code>Done!</code>
      </pre>
    </div>
  );
};

export default MockCode;
