/*
 * Filename: /home/<USER>/WorkStation/code-portfolio/components/LeftDrawer/RightBar.jsx
 * Path: /home/<USER>/WorkStation/code-portfolio
 * Created Date: Saturday, March 11th 2023, 12:17:07 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
"use client";
import Image from 'next/image';
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import downarrow from '../../../Assets/images/downarrow.svg';

const RightBar = () => {
  const pathname = usePathname();

  const handleFold = (e) => {
    const foldMain = e.target.closest(".fold-main");
    const foldChild = foldMain.nextElementSibling;
    const isHidden = foldChild.classList.contains("hidden");

    foldChild.classList.toggle("hidden", !isHidden);
    foldMain.classList.toggle("border-down", isHidden);
    foldMain.classList.toggle("border-up", !isHidden);
  };

  const handleActive = (href) => {
    return pathname === href ? "active" : "";
  };

  return (
    <>
      <div className="basis-10/12 min-h-full">
        <div className="w-full border-up border-down">
          <div onClick={handleFold} className="flex p-2 gap-2 fold-main">
            <Image src={downarrow} alt="tanzim077" />
            <p>professional_info</p>
          </div>
          <div className="border-up">
            <div className="pl-6 py-3 fold-child">
              <Link href="/who_am_i/experience">
                <p className={handleActive("/who_am_i/experience")}>
                  work_experience
                </p>
              </Link>
              <Link href="/who_am_i/skills">
                <p className={handleActive("/who_am_i/skills")}>skills</p>
              </Link>
              <Link href="/who_am_i/projects">
                <p className={handleActive("/who_am_i/projects")}>
                  my_projects
                </p>
              </Link>
            </div>
          </div>
        </div>
        <div className="w-full ">
          <div
            className="flex p-2 gap-2 border-down fold-main"
            onClick={handleFold}
          >
            <Image src={downarrow} alt="tanzim077" />
            <p className={handleActive()}>personal_info</p>
          </div>
          <div className="fold-child">
            <div className="pl-6 py-3 ">
              <Link href="/who_am_i/bio">
                <p className={handleActive("/who_am_i/bio")}>bio</p>
              </Link>
              <Link href="/who_am_i/interest">
                <p className={handleActive("/who_am_i/interest")}>interest</p>
              </Link>
              <Link href="/who_am_i/educations">
                <p className={handleActive("/who_am_i/educations")}>
                  education
                </p>
              </Link>
              <Link href="/who_am_i/contact">
                <p className={handleActive("/who_am_i/contact")}>contact_me</p>
              </Link>
              <Link href="/who_am_i/resume">
                <p className={handleActive("/who_am_i/resume")}>my_resume</p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RightBar;
