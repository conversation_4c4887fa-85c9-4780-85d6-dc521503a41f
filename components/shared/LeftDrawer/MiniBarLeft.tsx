/*
 * Filename: /home/<USER>/WorkStation/code-portfolio/components/LeftDrawer/MiniBarLeft.jsx
 * Path: /home/<USER>/WorkStation/code-portfolio
 * Created Date: Saturday, March 11th 2023, 12:16:55 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */
import React from 'react';
import terminal from '../../../Assets/images/terminal.svg';
import hobbies from '../../../Assets/images/hobbies-icon.svg';
import personal from '../../../Assets/images/personal.svg';
import Image from 'next/image';

const MiniBarLeft = () => {
  return (
    <>
      <div className=" border-right">
        <div className="flex flex-col p-3 items-center gap-3">
          <Image src={terminal} alt="tanzim077" />
          <Image src={personal} alt="tanzim077" />{" "}
          <Image src={hobbies} alt="tanzim077" />
        </div>
      </div>
    </>
  );
};

export default MiniBarLeft;
