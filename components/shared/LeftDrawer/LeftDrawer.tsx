"use client";
/*
 * Filename: /home/<USER>/WorkStation/code-portfolio/components/LeftDrawer/LeftDrawer.jsx
 * Path: /home/<USER>/WorkStation/code-portfolio
 * Created Date: Saturday, March 11th 2023, 12:15:26 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
import React from 'react';
import MiniBarLeft from './MiniBarLeft';
import RightBar from './RightBar';

const LeftDrawer = () => {
  return (
    <>
      <div class="basis-2/12 border-right min-h-full">
        <div class="flex flex-row min-h-full border-right">
          {/* Left mini Bar */}
          <MiniBarLeft />
          {/* right file list Bar */}
          <RightBar />
        </div>
      </div>
    </>
  );
};

export default LeftDrawer;
