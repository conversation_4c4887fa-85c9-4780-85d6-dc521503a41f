"use client";
/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/Navbar/Navbar.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Wednesday, March 15th 2023, 7:22:30 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
import Link from 'next/link';
import {usePathname} from 'next/navigation';
import './index.css';

const Navbar = () => {
  const pathname = usePathname();

  // a function for check which link is active
  const checkActiveLink = (path) => {
    if (pathname === path) {
      return "router-link-active";
    } else {
      return "";
    }
  };

  return (
    <header id="navbar" className="w-full hidden md:flex flex-col">
      <nav className="w-full flex justify-between border-bot">
        <div className="flex">
          <Link id="nav-logo" className={checkActiveLink("/")} href="/">
            tanzim-ahmed
          </Link>
          <Link
            id="nav-link"
            className={checkActiveLink("/hello")}
            href="/hello"
          >
            _hello{" "}
          </Link>
          <Link
            id="nav-link"
            // className={checkActiveLink("/who_am_i/experience")}
            className={checkActiveLink("/aboutme")}
            href="/aboutme"
          >
            _who_am_i{" "}
          </Link>
          <Link
            id="nav-link"
            className={checkActiveLink("/projects")}
            href="/projects"
            // href="/who_am_i/projects"
          >
            _projects{" "}
          </Link>
        </div>
        <Link
          id="nav-link-contact"
          className={checkActiveLink("/contact")}
          href="/contact"
        >
          _contact_me{" "}
        </Link>
      </nav>
    </header>
  );
};

export default Navbar;
