/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who_am_i/head.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 9:16:05 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tan<PERSON><PERSON>
 */
import React from 'react';

const head = () => {
  return (
    <>
      <title>Who is Tanzi<PERSON></title>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="description" content="<PERSON><PERSON><PERSON>'s Portfolio" />
      <meta name="author" content="<PERSON><PERSON><PERSON>" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap"
        rel="stylesheet"
      />
    </>
  );
};

export default head;
