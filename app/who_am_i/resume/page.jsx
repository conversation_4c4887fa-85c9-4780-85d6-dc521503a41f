/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who_am_i/projects/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 10:41:37 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

"use client";
/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who-am-i/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 6:52:54 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
import React from 'react';

const Page = () => {
  return (
    <>
      {/* <ComingSoon /> */}
      <object
        data="https://docs.google.com/document/d/1H0fKASdrnRoJtKZj5JiJYyDpjSO0fQ89cq6JSFwVhZs/edit?usp=sharing"
        type="application/pdf"
        width="100%"
        height="100%"
      >
        <p>
          Alternative text - include a link{" "}
          <a href="https://docs.google.com/document/d/1H0fKASdrnRoJtKZj5JiJYyDpjSO0fQ89cq6JSFwVhZs/edit?usp=sharing">
            to the PDF!
          </a>
        </p>
      </object>
    </>
  );
};

export default Page;
