/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who_am_i/bio/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 10:26:10 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

import React from 'react';
import CoverPhoto from './CoverPhoto';

const Page = () => {
  return (
    <>
      <div className="bio">
        <CoverPhoto />
        <div className="bio-text">
          <p>
            Hello, I'm <PERSON><PERSON><PERSON>, a dedicated Full Stack developer with a
            strong focus on creating strong and scalable solutions. With more
            than 1.5 years of professional experience in the industry, I
            specialize in designing and implementing efficient frameworks that
            support high-performance applications.
          </p>
          <br />
          <p>
            Throughout my career, I have acquired extensive knowledge and skills
            in developing web applications using advanced technologies and
            frameworks like React JS, NextJS, Express JS, and Node JS. I am
            proficient in various programming languages such as Javascript, C#,
            and Python. Additionally, I have a strong understanding of
            databases, API integrations, and cloud platforms like AWS, MongoDB,
            and GraphQL. I stay updated with the latest technology trends and
            tools.
          </p>
          <br />
          <p>
            When it comes to software development, I prioritize three important
            things: writing clean code, ensuring maintainability, and following
            a test-driven approach. I strongly believe in creating code that not
            only works but is also easy to understand and modify. I make it a
            point to adhere to best practices, conduct thorough code reviews,
            and constantly strive to improve the quality of the projects I work
            on.
          </p>
          <br />
          <p>
            Collaboration and effective communication play a crucial role in my
            work style. I enjoy working with diverse teams and leverage my
            strong interpersonal skills to bridge the gap between technical and
            non-technical team members. I take pride in translating complex
            requirements into practical solutions that meet the needs of users
            while aligning with the overall business objectives.
          </p>
          <br />
          <p>
            Besides my professional work, I have a strong passion for learning.
            I'm always enthusiastic about exploring new technologies and staying
            updated with the latest trends in the industry. I actively seek
            opportunities to expand my skill set and contribute to the
            open-source community. Currently, I'm looking for fresh challenges
            and exciting opportunities where I can apply my expertise in backend
            development to create innovative and impactful software solutions.
            If you have a project or opportunity that you'd like to discuss,
            please don't hesitate to get in touch. I'm eager to connect with
            you! Feel free to customize and modify the bio according to your
            preferences and specific experiences.
          </p>
        </div>
      </div>
    </>
  );
};

export default Page;
