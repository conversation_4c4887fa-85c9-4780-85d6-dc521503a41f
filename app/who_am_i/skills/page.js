"use client";
/*
 * File           : page.js
 * Project        : myportfolio
 * Created Date   : Mo 03 Apr 2023 01:57:23
 * Description    : <<description>>
 *
 *
 * Author         : <PERSON><PERSON><PERSON>
 * Email          : <EMAIL>
 * ----------------------
 * Last Modified  : Mon Apr 03 2023
 * Modified By    : <PERSON><PERSON><PERSON>
 * ------------------------
 */

import Aws from '/Assets/images/icons/Aws.svg';
import Csharp from '/Assets/images/icons/Csharp.svg';
import Docker from '/Assets/images/icons/Docker.svg';
import Graphql from '/Assets/images/icons/Graphql.svg';
import JavaScript from '/Assets/images/icons/JavaScript.svg';
import MongoDB from '/Assets/images/icons/MongoDB.svg';
import NodeJs from '/Assets/images/icons/NodeJs.svg';
import PostgreSQL from '/Assets/images/icons/PostgreSQL.svg';
import Python from '/Assets/images/icons/Python.svg';
import ReactIC from '/Assets/images/icons/React.svg';
import TypeScript from '/Assets/images/icons/TypeScript.svg';
import ASP from '/Assets/images/icons/asp.Net.svg';
import ExpressJs from '/Assets/images/icons/ex.png';
// import Next from "/Assets/images/icons/next.png";
import CustomProgressBar from '@/components/CustomProgressBar/CustomProgressBar';
import Image from 'next/image';
import Nest from '/Assets/images/icons/nest.svg';
import Next from '/Assets/images/icons/next.svg';

const frontendData = [
  { name: "React", icon: ReactIC, percentage: 85 },
  { name: "Next Js", icon: Next, percentage: 70 },
  // { name: "Html", icon: Html5, percentage: 70 },
  // { name: "Css3", icon: Css3, percentage: 70 },
  // { name: "Material UI", icon: MaterialUI, percentage: 70 },
];
const backendData = [
  { name: "NodeJs", icon: NodeJs, percentage: 85 },
  { name: "Express Js", icon: ExpressJs, percentage: 85 },
  { name: "ASP. net", icon: ASP, percentage: 70 },
  { name: "Nest Js", icon: Nest, percentage: 60 },
];
const extraData = [
  { name: "JavaScript", icon: JavaScript, percentage: 90 },
  { name: "Csharp", icon: Csharp, percentage: 80 },
  { name: "Python", icon: Python, percentage: 80 },
  { name: "TypeScript", icon: TypeScript, percentage: 70 },
  { name: "Docker", icon: Docker, percentage: 60 },
  { name: "AWS", icon: Aws, percentage: 60 },
  { name: "MongoDB", icon: MongoDB, percentage: 80 },
  { name: "PostgreSQL", icon: PostgreSQL, percentage: 70 },
  { name: "Graphql", icon: Graphql, percentage: 60 },
];

const Page = () => {
  return (
    <div className="flex full-div">
      <div className="border-right left-skills-div">
        <div className="skilltDiv">
          <h5 className="font-bold skill-title">front_end</h5>
          {backendData.map((item) => (
            <div
              key={item.name}
              className="flex gap-6 my-2 items-center justify-between"
            >
              <Image src={item.icon} alt={item.name} width={40} height={40} />
              <div>
                <div>{item.name}</div>
              </div>
              <CustomProgressBar percentage={item.percentage} />
            </div>
          ))}
        </div>

        <div className="skilltDiv">
          <h5 className="font-bold skill-title">back_end</h5>
          {frontendData.map((item) => (
            <div
              key={item.name}
              className="flex gap-6 my-2 items-center justify-between"
            >
              <Image src={item.icon} alt={item.name} width={40} height={40} />
              <div>
                <div>{item.name}</div>
              </div>
              <CustomProgressBar percentage={item.percentage} />
            </div>
          ))}
        </div>
      </div>
      <div className="right-skills-div">
        <div className="skilltDiv">
          <h5 className="font-bold skill-title">extra</h5>
          {extraData.map((item) => (
            <div
              key={item.name}
              className="flex gap-6 my-2 items-center justify-between"
            >
              <Image src={item.icon} alt={item.name} width={40} height={40} />
              <div>
                <div>{item.name}</div>
              </div>
              <CustomProgressBar percentage={item.percentage} />
            </div>
          ))}
        </div>
      </div>

      {/* <ComingSoon /> */}
    </div>
  );
};

export default Page;
